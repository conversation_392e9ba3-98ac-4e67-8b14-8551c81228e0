const { createSlice } = require('@reduxjs/toolkit');

const InitialValue = {
  fetching: true,
  data: [],
};

const OnboardedPatientsSlice = createSlice({
  name: 'OnboardedPatientsReducer',
  initialState: InitialValue,
  reducers: {
    setOnboardedPatientsFetching: (state, action) => {
      state.fetching = action.payload;
    },
    setOnboardedPatientsList: (state, action) => {
      state.data = action.payload || [];
    },
    resetOnboardedPatientsState: (state) => {
      state.fetching = InitialValue.fetching;
      state.data = InitialValue.data;
    },
  },
  extraReducers: (builder) => builder.addCase('RESET_STATE', () => InitialValue)
});

export const {
  setOnboardedPatientsFetching,
  setOnboardedPatientsList,
  resetOnboardedPatientsState,
} = OnboardedPatientsSlice.actions;

export default OnboardedPatientsSlice.reducer;
