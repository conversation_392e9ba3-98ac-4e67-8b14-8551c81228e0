import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  changePassword: false,
  changePasswordModal: false,
  smsModal: false,
  logout_loader: false,
  chatrefresh: 0,
  reminderHome: true,
  reminderPicker: true,
  isLoggedIn: false,
  fetchingUserAPI: true,
  userInfo: {
    lname: '',
    fname: '',
    dob: '',
    gender: '',
    mobile: '',
    photo_base64: '',
    chart_number: '',
    payer1_policy: '',
    payer1_name: '',
    payer2_policy: '',
    payer2_name: '',
    payer3_policy: '',
    payer3_name: '',
    device_token: '',
    email: '',
    patient_id: '',
    email_address: '',
    is_terms_accepted: true,
    togglepractice: '',
    role: '',
    reminder_preference: {
      email: '',
      email_preferece_time: '',
      notification: '',
      notification_preference_time: '',
      sms: '',
      sms_preference_time: '',
      voice: '',
      voice_preference_time: '',
    },
    toggleLoader: false,
    hourLoader: false,
    dayLoader: false,
  },
  staterefresh: 0,
  hrs: 0,
  mins: 0,
  days: 0,
  type: '',
  contact_data: '',
  switch_robo: false,
  switch_sms: false,
  switch_email: false,
  switch_notification: false,
  changed: false,
  original_data: {
    original_email: '',
    original_email_preferece_time: '',
    original_notification: '',
    original_notification_preference_time: '',
    original_sms: '',
    original_sms_preference_time: '',
    original_voice: '',
    original_voice_preference_time: '',
  },
  ismodal: false,
  changes: {
    email: false,
    sms: false,
    notification: false,
    robocall: false,
  },
};

const UserSlice = createSlice({
  name: 'userInfoSlice',
  initialState: initialState,
  reducers: {
    setUserInfo: (state, action) => {
      state.userInfo.lname = action.payload.lname;
      state.userInfo.fname = action.payload.fname;
      state.userInfo.dob = action.payload.dob;
      state.userInfo.gender = action.payload.gender;
      state.userInfo.mobile = action.payload.mobile;
      state.userInfo.photo_base64 = action.payload.photo_base64;
      state.userInfo.email_address = action.payload.email_address;
      state.userInfo.address_line1 = action.payload.address_line1;
      state.userInfo.city = action.payload.city;
      state.userInfo.zip = action.payload.zip;
      state.userInfo.state = action.payload.state;
      state.userInfo.chart_number = action.payload.chartdata;
      state.userInfo.payer1_name = action.payload.payer1_name;
      state.userInfo.payer1_policy = action.payload.payer1_policy;
      state.userInfo.payer2_name = action.payload.payer2_name;
      state.userInfo.payer2_policy = action.payload.payer2_policy;
      state.userInfo.payer3_name = action.payload.payer3_name;
      state.userInfo.payer3_policy = action.payload.payer3_policy;
      state.userInfo.email = action.payload.email;
      state.userInfo.is_terms_accepted = action.payload.is_terms_accepted;
      state.userInfo.reminder_preference.email =
        action.payload.reminder_preference.email;
      state.userInfo.reminder_preference.email_preferece_time =
        action.payload.reminder_preference.email_preference_time;
      state.userInfo.reminder_preference.notification =
        action.payload.reminder_preference.notification;
      state.userInfo.reminder_preference.notification_preference_time =
        action.payload.reminder_preference.notification_preference_time;
      state.userInfo.reminder_preference.sms =
        action.payload.reminder_preference.sms;
      state.userInfo.reminder_preference.sms_preference_time =
        action.payload.reminder_preference.sms_preference_time;
      state.userInfo.reminder_preference.voice =
        action.payload.reminder_preference.voice;
      state.userInfo.reminder_preference.voice_preference_time =
        action.payload.reminder_preference.voice_preference_time;
      state.userInfo.patient_id = action.payload.patient_id;
      state.userInfo.role = action.payload.role || '';
      state.original_data.original_email =
        action.payload.reminder_preference.email;
      state.original_data.original_email_preferece_time =
        action.payload.reminder_preference.email_preference_time;
      state.original_data.original_notification =
        action.payload.reminder_preference.notification;
      state.original_data.original_notification_preference_time =
        action.payload.reminder_preference.notification_preference_time;
      state.original_data.original_sms = action.payload.reminder_preference.sms;
      state.original_data.original_sms_preference_time =
        action.payload.reminder_preference.sms_preference_time;
      state.original_data.original_voice =
        action.payload.reminder_preference.voice;
      state.original_data.original_voice_preference_time =
        action.payload.reminder_preference.voice_preference_time;
    },
    setLogoutLoader: (state, action) => {
      state.logout_loader = action.payload;
    },
    setUserLogin: (state, action) => {
      state.isLoggedIn = action.payload.isLoggedIn;
    },
    setFetchUser: (state, action) => {
      state.fetchingUserAPI = action.payload;
    },
    resetUserState: state => {
      console.warn('resetting user state');
      state.fetchingUserAPI = initialState.fetchingUserAPI;
      state.isLoggedIn = initialState.isLoggedIn;
      state.userInfo = initialState.userInfo;
    },
    setDeviceToken: (state, action) => {
      state.userInfo.device_token = action.payload;
    },
    settogglepractice: (state, action) => {
      state.userInfo.togglepractice = action.payload;
    },
    setReminderEmailTime: (state, action) => {
      state.userInfo.reminder_preference.email_preferece_time = action.payload;
    },
    setReminderSmsTime: (state, action) => {
      state.userInfo.reminder_preference.sms_preference_time = action.payload;
    },
    setReminderNotificationTime: (state, action) => {
      state.userInfo.reminder_preference.notification_preference_time =
        action.payload;
    },
    setReminderVoiceTime: (state, action) => {
      state.userInfo.reminder_preference.voice_preference_time = action.payload;
    },
    setReminderEmailToggle: (state, action) => {
      state.userInfo.reminder_preference.email = action.payload;
    },
    setReminderSmsToggle: (state, action) => {
      state.userInfo.reminder_preference.sms = action.payload;
    },
    setReminderVoiceToggle: (state, action) => {
      state.userInfo.reminder_preference.voice = action.payload;
    },
    setReminderPushNotificationToggle: (state, action) => {
      state.userInfo.reminder_preference.notification = action.payload;
    },
    setToggleLoader: (state, action) => {
      state.toggleLoader = action.payload;
    },
    setHourLoader: (state, action) => {
      state.hourLoader = action.payload;
    },
    setDayLoader: (state, action) => {
      state.dayLoader = action.payload;
    },
    setStateRefresh: (state, action) => {
      state.staterefresh = action.payload;
    },
    setHrs: (state, action) => {
      state.hrs = action.payload;
    },
    setMins: (state, action) => {
      state.mins = action.payload;
    },
    setDays: (state, action) => {
      state.days = action.payload;
    },
    setType: (state, action) => {
      state.type = action.payload;
    },
    setConatctData: (state, action) => {
      state.contact_data = action.payload;
    },
    setSwitchRobo: (state, action) => {
      state.switch_robo = action.payload;
    },
    setSwitchSms: (state, action) => {
      state.switch_sms = action.payload;
    },
    setSwitchEmail: (state, action) => {
      state.switch_email = action.payload;
    },
    setSwitchNotification: (state, action) => {
      state.switch_notification = action.payload;
    },
    setChanged: (state, action) => {
      state.changed = action.payload;
    },
    setIsModal: (state, action) => {
      state.ismodal = action.payload;
    },
    setChatRefresh: (state, action) => {
      state.chatrefresh = action.payload;
    },
    setReminderHome: (state, action) => {
      state.reminderHome = action.payload;
    },
    setReminderPicker: (state, action) => {
      state.reminderPicker = action.payload;
    },
    setEmailChanges: (state, action) => {
      state.changes.email = action.payload;
    },
    setSmsChanges: (state, action) => {
      state.changes.sms = action.payload;
    },
    setNotificationChanges: (state, action) => {
      state.changes.notification = action.payload;
    },
    setRobocallChanges: (state, action) => {
      state.changes.robocall = action.payload;
    },
    setOriginalValue: (state, action) => {
      state.original_data.original_email = action.payload.email;
      state.original_data.original_email_preferece_time =
        action.payload.email_preference_time;
      state.original_data.original_notification = action.payload.notification;
      state.original_data.original_notification_preference_time =
        action.payload.notification_preference_time;
      state.original_data.original_sms = action.payload.sms;
      state.original_data.original_sms_preference_time =
        action.payload.sms_preference_time;
      state.original_data.original_voice = action.payload.voice;
      state.original_data.original_voice_preference_time =
        action.payload.voice_preference_time;
    },
    setSmsModal: (state, action) => {
      state.smsModal = action.payload;
    },
    setChangePassword: (state, action) => {
      state.changePassword = action.payload;
    },
    setChangePasswordModal: (state, action) => {
      state.changePasswordModal = action.payload;
    },
  },
  extraReducers:(builder)=>builder.addCase('RESET_STATE', () => {
    return initialState
  })
});

export const {
  setUserInfo,
  resetUserState,
  setFetchUser,
  setPracticeinfo,
  setUserLogin,
  setDeviceToken,
  settogglepractice,
  setReminderEmailTime,
  setReminderSmsTime,
  setReminderNotificationTime,
  setReminderVoiceTime,
  setReminderEmailToggle,
  setReminderSmsToggle,
  setReminderVoiceToggle,
  setReminderPushNotificationToggle,
  setToggleLoader,
  setHourLoader,
  setDayLoader,
  setStateRefresh,
  setHrs,
  setMins,
  setDays,
  setType,
  setConatctData,
  setSwitchRobo,
  setSwitchSms,
  setSwitchEmail,
  setSwitchNotification,
  setChanged,
  setIsModal,
  setChatRefresh,
  setReminderHome,
  setReminderPicker,
  setEmailChanges,
  setSmsChanges,
  setNotificationChanges,
  setRobocallChanges,
  setOriginalValue,
  setLogoutLoader,
  setSmsModal,
  setChangePassword,
  setChangePasswordModal,
} = UserSlice.actions;

export default UserSlice.reducer;
