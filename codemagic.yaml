workflows:
  react-native-android:
    name: CHC Android InternalRelease
    max_build_duration: 30
    instance_type: mac_mini_m1
    environment:
      android_signing:
        - keystore_reference
      groups:
        - google_play
      vars:
        PACKAGE_NAME: 'com.coherent.clearhealthcare'
        VERSION_CODE: 41
        VERSION_NAME: '0.7.1'
    labels:
      - CHC DEV ANDROID BUILD(INTERNAL RELEASE)
    scripts:
      - name: Set Android SDK location
        script: |
          echo "sdk.dir=$ANDROID_SDK_ROOT" > "$CM_BUILD_DIR/android/local.properties"
      - name: Install node dependencies
        script: |
          yarn
      - name: Build Android release
        script: |
          cd android 
          ./gradlew clean 
          ./gradlew assembleRelease
    artifacts:
      - android/app/build/outputs/**/*.apk
    publishing:
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
        notify:
          success: true
          failure: false

  react-native-android-prod:
    name: CHC Android ProductionRelease
    max_build_duration: 30
    instance_type: mac_mini_m1
    environment:
      android_signing:
        - keystore_reference
      groups:
        - google_play
      vars:
        PACKAGE_NAME: 'com.coherent.clearhealthcare'
        VERSION_CODE: 41
        VERSION_NAME: '0.7.1'
    labels:
      - CHC APP PROD RELEASE(ANDROID)
    scripts:
      - name: Set Android SDK location
        script: |
          echo "sdk.dir=$ANDROID_SDK_ROOT" > "$CM_BUILD_DIR/android/local.properties"
      - name: Install node dependencies
        script: |
          yarn
      - name: Build Android release
        script: |
          cd android 
          ./gradlew clean 
          ./gradlew bundleRelease
    artifacts:
      - android/app/build/outputs/**/*.aab
    publishing:
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
        notify:
          success: true
          failure: true
      google_play:
        credentials: $GCLOUD_SERVICE_ACCOUNT_CREDENTIALS
        track: production
        submit_as_draft: true

  react-native-ios:
    name: CHC ios InternalRelease
    max_build_duration: 120
    instance_type: mac_mini_m1
    integrations:
      app_store_connect: CHC Codemagic Connect API Key # <-- Get the name from Codemagic > Teams > Coherent Teams > Team Integrations > Manage Keys (Developer portal) > Copy the key
    environment:
      xcode: 16.0
      groups:
        - IOS App Store
      ios_signing:
        provisioning_profiles:
          - clear_healthcare_ios_appstore
        certificates:
          - chc_codemagic_ios_signing_cert_latest
      vars:
        BUNDLE_ID: 'com.coherent.clearhealthcare'
        XCODE_WORKSPACE: 'CHC.xcworkspace' # Name of the Xcode workspace
        XCODE_SCHEME: 'CHC' # <-- Name of the Xcode scheme
        APP_STORE_APPLE_ID: $IOS_APP_STORE_APPLE_ID # <-- App Store ID (get it from Apple developer portal)
        BRANCH_NAME: $CM_BRANCH # <-- Codemagic provides the branch name in this variable
        VERSION_CODE: 41 # Set the build number here
        VERSION_NAME: '0.7.1'
    labels:
      - CHC DEV build(ios)
    scripts:
      - name: Install node dependencies
        script: |
          yarn
      - name: Set Info.plist values
        script: |
          cd ios
          PLIST=$CM_BUILD_DIR/GoogleService-Info.plist
          PLIST_BUDDY=/usr/libexec/PlistBuddy
          $PLIST_BUDDY -c "Add :ITSAppUsesNonExemptEncryption bool false" $PLIST
          cd ..
      - name: Set Info.plist values
        script: |
          cd ios
          PLIST=$CM_BUILD_DIR/CHC/Info.plist
          PLIST_BUDDY=/usr/libexec/PlistBuddy
          # Set the version number and build number
          $PLIST_BUDDY -c "Set :CFBundleShortVersionString $VERSION_NAME" $PLIST
          $PLIST_BUDDY -c "Set :CFBundleVersion $VERSION_CODE" $PLIST
          cd ..
      - name: Clean CocoaPods cache
        script: |
          cd ios
          pod cache clean --all
          cd ..
      - name: Install Pods
        script: |
          cd ios
          pod update hermes-engine --no-repo-update
          pod install --repo-update
          cd ..
      - name: Set up provisioning profiles settings on Xcode project
        script: |
          cd ios
          xcode-project use-profiles
          cd ..
      # - name: Increment build number
      #   script: |
      #     cd $CM_BUILD_DIR/ios
      #     LATEST_BUILD_NUMBER=$(app-store-connect get-latest-testflight-build-number "$IOS_APP_STORE_APPLE_ID")
      #     agvtool new-version -all $(($LATEST_BUILD_NUMBER + 1))
      - name: Build ipa for distribution
        script: |
          xcode-project build-ipa \
            --workspace "$CM_BUILD_DIR/ios/$XCODE_WORKSPACE" \
            --scheme "$XCODE_SCHEME"
    artifacts:
      - build/ios/ipa/*.ipa
      - /tmp/xcodebuild_logs/*.log
      - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.app
      - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.dSYM
    publishing:
      app_store_connect:
        auth: integration
        submit_to_testflight: true
        # beta_groups: # Specify the names of beta tester groups that will get access to the build once it has passed beta review.
        #   - CHC Tester Group1
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
        notify:
          success: true
          failure: false

  react-native-ios-prod:
    name: CHC ios ProductionRelease
    max_build_duration: 120
    instance_type: mac_mini_m1
    integrations:
      app_store_connect: CHC Codemagic Connect API Key # <-- Get the name from Codemagic > Teams > Coherent Teams > Team Integrations > Manage Keys (Developer portal) > Copy the key
    environment:
      xcode: 16.0
      groups:
        - IOS App Store
      ios_signing:
        provisioning_profiles:
          - clear_healthcare_ios_appstore
        certificates:
          - chc_codemagic_ios_signing_cert_latest
      vars:
        BUNDLE_ID: 'com.coherent.clearhealthcare'
        XCODE_WORKSPACE: 'CHC.xcworkspace' # Name of the Xcode workspace
        XCODE_SCHEME: 'CHC' # <-- Name of the Xcode scheme
        APP_STORE_APPLE_ID: $IOS_APP_STORE_APPLE_ID # <-- App Store ID (get it from Apple developer portal)
        BRANCH_NAME: $CM_BRANCH # <-- Codemagic provides the branch name in this variable
        VERSION_CODE: 41 # Set the build number here
        VERSION_NAME: '0.7.1'
    labels:
      - CHC APP PROD RELEASE(ios)
    scripts:
      - name: Show Xcode version
        script: |
          xcodebuild -version
      - name: Install node dependencies
        script: |
          yarn
      - name: Set GoogleService-Info.plist values
        script: |
          cd ios
          PLIST=$CM_BUILD_DIR/GoogleService-Info.plist
          PLIST_BUDDY=/usr/libexec/PlistBuddy
          $PLIST_BUDDY -c "Add :ITSAppUsesNonExemptEncryption bool false" $PLIST
          cd ..
      - name: Set Info.plist values
        script: |
          cd ios
          # Correct the path if necessary
          PLIST="CHC/Info.plist" # Update this to the correct path
          PLIST_BUDDY=/usr/libexec/PlistBuddy
          # Check if the plist file exists
          if [ ! -f "$PLIST" ]; then
            echo "Error: $PLIST does not exist."
            exit 1
          fi
          # Set the version number and build number
          $PLIST_BUDDY -c "Set :CFBundleShortVersionString $VERSION_NAME" $PLIST
          $PLIST_BUDDY -c "Set :CFBundleVersion $VERSION_CODE" $PLIST
          # Set other Info.plist values
          $PLIST_BUDDY -c "Add :ITSAppUsesNonExemptEncryption bool false" $PLIST
          cd ..
      - name: Clean CocoaPods cache
        script: |
          cd ios
          pod cache clean --all
          cd ..
      - name: Install Pods
        script: |
          cd ios
          pod update hermes-engine --no-repo-update
          pod install --repo-update
          cd ..
      - name: Set up provisioning profiles settings on Xcode project
        script: |
          cd ios
          xcode-project use-profiles
          cd ..
      # - name: Increment build number
      #   script: |
      #     cd $CM_BUILD_DIR/ios
      #     LATEST_BUILD_NUMBER=$(app-store-connect get-latest-testflight-build-number "$IOS_APP_STORE_APPLE_ID")
      #     agvtool new-version -all $(($LATEST_BUILD_NUMBER + 1))
      - name: Build ipa for distribution
        script: |
          xcode-project build-ipa \
            --workspace "$CM_BUILD_DIR/ios/$XCODE_WORKSPACE" \
            --scheme "$XCODE_SCHEME"
    artifacts:
      - build/ios/ipa/*.ipa
      - /tmp/xcodebuild_logs/*.log
      - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.app
      - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.dSYM
    publishing:
      app_store_connect:
        auth: integration
        # submit_to_testflight: true
        # beta_groups: # Specify the names of beta tester groups that will get access to the build once it has passed beta review.
        #   - CHC Tester Group1
        submit_to_app_store: true
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
        notify:
          success: true
          failure: true
