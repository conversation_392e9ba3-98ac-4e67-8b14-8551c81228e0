{"name": "chc", "version": "0.7.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "android-build": "cd android && ./gradlew clean && ./gradlew assembleRelease", "pod": "cd ios && pod install"}, "dependencies": {"@apollo/client": "^3.11.1", "@fortawesome/fontawesome-free": "^6.6.0", "@fortawesome/fontawesome-free-solid": "^5.0.13", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@fortawesome/react-native-fontawesome": "^0.3.2", "@gorhom/bottom-sheet": "^4.6.4", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/geolocation": "^3.3.0", "@react-native-community/image-editor": "^4.3.0", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-community/slider": "^4.5.2", "@react-native-firebase/analytics": "^20.3.0", "@react-native-firebase/app": "^20.3.0", "@react-native-firebase/crashlytics": "^20.3.0", "@react-native-firebase/messaging": "^20.3.0", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.7.7", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/material-top-tabs": "^6.6.14", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.1", "@reduxjs/toolkit": "^2.2.7", "@sentry/react-native": "^5.26.0", "@use-it/interval": "^1.0.0", "axios": "^1.7.2", "deprecated-react-native-prop-types": "^5.0.0", "formik": "^2.4.6", "graphql": "^16.9.0", "import": "^0.0.6", "md5": "^2.3.0", "moment": "^2.30.1", "react": "18.3.1", "react-i18next": "^15.0.0", "react-native": "0.74.3", "react-native-animatable": "^1.4.0", "react-native-autocomplete-dropdown": "^3.1.5", "react-native-background-timer": "^2.4.1", "react-native-blob-util": "^0.22.0", "react-native-check-version": "^1.3.0", "react-native-date-picker": "^5.0.10", "react-native-device-info": "^14.0.4", "react-native-devsettings": "^1.0.5", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.17.1", "react-native-gifted-chat": "^2.4.0", "react-native-image-picker": "^7.1.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-maps": "1.17.3", "react-native-maps-directions": "^1.9.0", "react-native-navigation-hooks": "^6.3.0", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "^6.3.3", "react-native-pdf": "^6.7.5", "react-native-push-notification": "^8.1.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "3.16.7", "react-native-redash": "^18.1.3", "react-native-safe-area-context": "^4.10.8", "react-native-screens": "^3.32.0", "react-native-searchable-dropdown": "^1.1.3", "react-native-snap-carousel": "^3.9.1", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.4.0", "react-native-swipe-gestures": "^1.0.5", "react-native-track-player": "^4.1.1", "react-native-vector-icons": "^10.1.0", "react-native-video": "^6.10.0", "react-native-vision-camera": "^4.6.4", "react-native-webview": "13.10.5", "react-native-wheel-scrollview-picker": "^2.0.4", "react-redux": "^9.1.2", "redux": "^5.0.1"}, "devDependencies": {"@babel/core": "^7.24.9", "@babel/preset-env": "^7.25.0", "@babel/runtime": "^7.25.0", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@react-native/typescript-config": "0.74.85", "@types/react": "^18.3.3", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.7.0", "eslint": "^9.8.0", "jest": "^29.7.0", "prettier": "3.3.3", "react-test-renderer": "18.3.1", "typescript": "5.5.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}