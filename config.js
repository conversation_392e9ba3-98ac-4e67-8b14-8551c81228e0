const config = {
  STAGING: 'PROD',
  DEV: {
    COGNITO_URL: 'https://api-clinicweb-dev.clearhealthcare.ai/',
    PATIENT_INFO: 'https://api-clinicweb-dev.clearhealthcare.ai/patient/',
    ADMIN_API: 'https://nviv233wt1.execute-api.us-east-2.amazonaws.com/prod/patient/',
    BITRIX_CHAT_URL:
      'https://clinicweb-dev.clearhealthcare.ai/commonnew/chat.html',
    TERMS_AND_CONDITIONS:
      'https://clinicweb-dev.clearhealthcare.ai/privacy-policy.html',
    INTRO_VIDEO: 'https://clinicweb-dev.clearhealthcare.ai/commonnew/intro.mp4',
    INTRO_PHYSICIANS: 'https://clinicweb-dev.clearhealthcare.ai/commonnew/output.json'

  },
  PROD: {
    COGNITO_URL: 'https://api-clinicweb.clearhealthcare.ai/',
    PATIENT_INFO: 'https://api-clinicweb.clearhealthcare.ai/patient/',
    ADMIN_API: 'https://nviv233wt1.execute-api.us-east-2.amazonaws.com/prod/patient/',
    BITRIX_CHAT_URL: 'https://clinicweb.clearhealthcare.ai/commonnew/chat.html',
    TERMS_AND_CONDITIONS:
      'https://clinicweb.clearhealthcare.ai/privacy-policy.html',
    INTRO_VIDEO: 'https://clinicweb-dev.clearhealthcare.ai/commonnew/intro.mp4',
    INTRO_PHYSICIANS: 'https://clinicweb-dev.clearhealthcare.ai/commonnew/output.json'
  },
  APPSYNC_HTTP:
    'https://qesrkljau5guffvdwkojozp7qe.appsync-api.us-east-2.amazonaws.com/graphql',
  BITRIXREG:
    'https://portal-dev.coherenteyecare.com/rest/batch.json?logTag=widget.init.config&timType=livechat&timDevice=web',
};



export default config;
