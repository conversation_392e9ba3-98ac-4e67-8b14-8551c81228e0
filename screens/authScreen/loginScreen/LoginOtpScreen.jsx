import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  SafeAreaView,
  Keyboard,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import {  ResendOtpMobile } from '../../../hooks/createAccount';
import { Button } from '../../ui/components/Button';
import { CodeEntry } from '../../ui/components/CodeEntry';
import CustomText from '../../ui/components/customText/customText';
import { Header } from '../../ui/components/Header';
import { ErrorMsgHandler } from '../../ui/components/MessageHandler/messageHandler';
import { TextComponent } from '../../ui/components/TextComponent';
import { Theme, useThemeContext } from '../../ui/theme';
import { theme } from '../../ui/theme/default/theme';
import moment from 'moment';
import { loginOtpConfirmHook } from '../../../hooks/otpLogin';
import { setUserInfo, setUserLogin, setFetchUser } from '../../../stores/userReducer';

const LoginOtpScreen = ({ route, navigation }) => {
  const [values, setValues] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState(false);
  const [counter, setCounter] = useState(120);
  const [startTime, setStartTime] = useState(moment().format('MM/DD/YYYY HH:mm:ss'))
  const [preloader, setPreloader] = useState(false);
  const [Resendotppreloader, setResendotppreloader] = useState(false);
  const [errorModal, setErrorModal] = useState(false);
  const [responseErrorMsg, setresponseErrorMsg] = useState(false);
  const [DOB, mobile ] = [route.params.DOB,route.params.mobile];
  const dispatch = useDispatch();

  const updateState = (index, digit) => {
    let newValues = values.map((item, itemIndex) => {
      if (itemIndex !== index) return item;
      return digit;
    });
    setValues(newValues);
  };

  const handleAnimationEnd = () => {
    setValues(['', '', '', '', '', '']);
  };

  const validation = () => {
    let result = false;
    values.forEach(item => {
      if (item === '') result = true;
    });
    return result;
  };

  const handleResend = async () => {
    setResendotppreloader(true);
    const resendOtpHandler = await ResendOtpMobile(route.params?.mobile, route.params?.DOB);
    if (resendOtpHandler) Alert.alert('Otp Sent Successfully');

    setValues(['', '', '', '', '', '']);
    setStartTime(moment().format('MM/DD/YYYY HH:mm:ss'));
    setCounter(120);
    setButtonClickable(false);
    setHere(prev => !prev);
    setResendotppreloader(false);
  };

  const handleSubmit = async () => {
    try {
      setPreloader(true);
      let code = values[0] + values[1] + values[2] + values[3] + values[4] + values[5];
      if(code.length === 6){
        const patientInfo = await loginOtpConfirmHook(
          mobile,
          DOB,
          code,
          setErrorModal,
          setresponseErrorMsg,
          dispatch
        );

        if (patientInfo && patientInfo.userInfo) {
          const userInfo = patientInfo.userInfo;
          dispatch(setFetchUser(false));
          dispatch(setUserInfo(userInfo));
          dispatch(setUserLogin({ isLoggedIn: true }));

          console.log('=== LOGIN NAVIGATION DEBUG ===');
          console.log('userInfo.role:', userInfo.role);

          // Check if user role is admin (case insensitive)
          const userRole = userInfo.role ? userInfo.role.toLowerCase() : '';
          console.log('userRole (lowercase):', userRole);

          // Add a small delay to ensure AsyncStorage operations complete
          await new Promise(resolve => setTimeout(resolve, 100));

          if (userRole === 'admin') {
            console.log('Navigating to AdminHomeScreen');
            // Navigate to admin panel
            navigation.reset({
              index: 0,
              routes: [{ name: 'HomeStack', params: { screen: 'AdminHomeScreen' } }],
            });
          } else {
            console.log('Navigating to regular HomeStack');
            // Navigate to regular home screen
            navigation.reset({
              index: 0,
              routes: [{ name: 'HomeStack' }],
            });
          }
          console.log('=== END LOGIN NAVIGATION DEBUG ===');
        }
        return;
      }
      setresponseErrorMsg("Please enter the Valid OTP");
      setErrorModal(true);
      return;
    } catch (err) {
      console.log("Error lock", err);
    } finally {
      setPreloader(false);
    }
  };

  const { s } = useThemeContext(createStyle);

  const [buttonClickable, setButtonClickable] = useState(false);
  const [here, setHere] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = moment(Date.now()).format('MM/DD/YYYY HH:mm:ss');
      const then_now = diffTime(startTime, now);

      setCounter(lastTimerCount => {
        if (then_now <= 0) {
          clearInterval(interval);
          setButtonClickable(true);
          return 0;
        }
        return then_now;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime, here]);

  const diffTime = (then, now) => {
    const ms = moment(now, 'MM/DD/YYYY HH:mm:ss')
      .diff(moment(then, 'MM/DD/YYYY HH:mm:ss'));

    if (ms > 119000) { // 120 seconds = 120,000 ms
      return 0;
    } else {
      const secondsElapsed = parseInt(moment.utc(ms).format('ss')) + (parseInt(moment.utc(ms).format('mm')) * 60);
      return 120 - secondsElapsed;
    }
  };


  if (preloader) {
    return (
      <View style={s?.preloaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <View style={s?.container}>
      {/* <View style={{ flex: 1, position: 'absolute', left: 0 }}>
        <TopBackground />
      </View> */}
      <TouchableWithoutFeedback
        onPress={() => Keyboard.dismiss()}
        style={{ flex: 1 }}>
        <SafeAreaView style={{ flex: 1, marginBottom: 15 }}>
          <Header onBackPress={() => navigation.goBack()} />
          <View style={[s?.mainCont]}>
            <TextComponent
              headerText={'Enter the code'}
              bodyText={`To verify your account please enter the code sent to ${mobile} !`}
              onPress={() => { }}
            />
            <View style={{ height: 60 }} />
            {Resendotppreloader ? (
              <ActivityIndicator size="large" color="#0000ff" />
            ) : (
              <>
                <CodeEntry
                  values={values}
                  onChangeInput={updateState}
                  textColor={theme.colors.white}
                  borderColorError={theme.colors.white}
                  textColorError={theme.colors.white}
                  bgColorUnactive={theme.colors.white}
                  borderColorUnactive={'#C4C4C4'}
                  borderColorActive={theme.colors.brandPrimary}
                  showAnimation={error}
                  onAnimationEnd={() => handleAnimationEnd()}
                  error={error}
                  setValues={setValues}
                />
                {!buttonClickable ?
                <TouchableOpacity
                  onPress={handleResend}
                  disabled={!buttonClickable}
                  style={{ marginTop: 25, flexDirection: 'row' }}>
                  <CustomText
                    style={[
                      s?.resendText,
                      counter === 0 && { color: theme.colors.brandPrimary },
                    ]}>
                    Resend in { '' }
                  </CustomText>
                  <CustomText
                    style={[
                      s?.resendText,
                      counter === 0 && { color: theme.colors.brandPrimary },
                      { width: 28, textAlign: 'center' }
                    ]}>{counter} </CustomText>
                  <CustomText
                    style={[
                      s?.resendText,
                      counter === 0 && { color: theme.colors.brandPrimary },
                    ]}>{ '' }seconds</CustomText>
                </TouchableOpacity>
                :
                <TouchableOpacity
                onPress={handleResend}
                disabled={!buttonClickable}
                style={{ marginTop: 25, flexDirection: 'row' }}>
                <CustomText
                  style={[
                    s?.resendText,
                    counter === 0 && { color: theme.colors.brandPrimary },
                  ]}>
                  Resend OTP
                </CustomText>

              </TouchableOpacity>
}
              </>
            )}
          </View>
          <View style={s?.buttonCont}>
            <Button
              disabled={validation()}
              onPress={() => handleSubmit()}
              fontWeight="600"
              color={theme.colors.white}
              text="Confirm"
              backgroundColor={theme.colors.brandPrimary}
            />
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
      {/* message handler */}
      <ErrorMsgHandler
        HandleModal={errorModal}
        setHandleModal={setErrorModal}
        msg={responseErrorMsg}
        clickedOk={() => { }}
      />
    </View>
  );
};

const createStyle = (theme: Theme) =>
  StyleSheet.create({
    container: {
      width: theme.metrics.width,
      height: '100%',
      backgroundColor: theme.colors.brandBackground,
    },
    buttonCont: {
      alignItems: 'center',
      alignSelf: 'center',
      width: theme.metrics.width - 30,
    },
    mainCont: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
    },
    link: {
      color: theme.colors.brandPrimary,
      fontSize: 16,
      fontWeight: '600',
    },
    notAMember: {
      fontWeight: '500',
      fontSize: 16,
      color: '#7D7D7D',
      marginVertical: 20,
    },
    resendText: {
      fontFamily: theme.fonts.monserat.SemiBold,
      color: theme.colors.gray,
      fontSize: 16,
    },
    preloaderContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

export default LoginOtpScreen;
