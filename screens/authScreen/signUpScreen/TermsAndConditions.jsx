// import React, {useEffect, useRef, useState} from 'react';
// import {
//   View,
//   Text,
//   ScrollView,
//   Dimensions,
//   TouchableOpacity,
//   ActivityIndicator,
// } from 'react-native';
// import {theme} from '../../ui/theme/default/theme';
// import {axiosPublic} from '../../../api/axiosPublic';
// import config from '../../../config';
// import PlainLoader from '../../Plainloader/PlainLoader';
// import WebView from 'react-native-webview';
// import axios from 'axios';
// import {event} from 'react-native-reanimated';
// import {axiosPrivate} from '../../../api/axiosPrivate';
// import GetPracticeInfo from '../../../helpers/getPracticeInfo';
// import {DefaultModal} from '../../ui/components/Modals/DefaultModal';
// import HandleLogout from '../../../helpers/logout';
// import {useDispatch} from 'react-redux';
// import LogoutIcon from './assets/logoutIcon';
// import CustomText from '../../ui/components/customText/customText';

// const TermsAndConditions = ({route, navigation}) => {
//   const [accepted, setAccepted] = useState(false);
//   const {width, height} = Dimensions.get('window');
//   const webviewRef = useRef();
//   const [tac, setTac] = useState('');
//   const [modal, setModal] = useState(false);
//   const [inTransact, setInTransact] = useState(false);
//   const [patientInfo, setPatientInfo] = useState(null);

//   const {params} = route;
//   const dispatch = useDispatch();

//   useEffect(() => {
//     (async () => {
//       const termsAndCond = await axios.get(
//         config[config.STAGING].COGNITO_URL + 'terms-and-conditions',
//       );
//       setTac(termsAndCond.data.message);
//     })();
//   }, []);

//   const scrollIndicatorScript = `
//   (function() {
//     var indicator = document.createElement('div');
//     indicator.innerHTML = '⬇ Scroll Down ⬇';
//     indicator.style.position = 'fixed';
//     indicator.style.bottom = '20px';
//     indicator.style.left = '50%';
//     indicator.style.transform = 'translateX(-50%)';
//     indicator.style.fontSize = '18px';
//     indicator.style.fontWeight = 'bold';
//     indicator.style.color = '#fff';
//     indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
//     indicator.style.padding = '12px 18px';
//     indicator.style.borderRadius = '30px';
//     indicator.style.boxShadow = '0px 4px 10px rgba(0, 0, 0, 0.2)';
//     indicator.style.zIndex = '9999';
//     indicator.style.cursor = 'pointer';
//     indicator.style.transition = 'all 0.3s ease';
//     indicator.style.animation = 'bounce 1.5s infinite';
//     indicator.style.opacity = '1';
//     document.body.appendChild(indicator);

//     var isScrolling = false;
//     var scrollAnimationFrame;
//     var originalText = '⬇ Scroll Down ⬇';
//     var isAtBottom = false;

//     // Function to check if user is at the bottom of the page
//     function checkScrollPosition() {
//       var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
//       var windowHeight = window.innerHeight;
//       var documentHeight = document.documentElement.scrollHeight;
//       var threshold = 50; // Show button when user scrolls up 50px from bottom

//       var atBottom = scrollTop + windowHeight >= documentHeight - threshold;

//       if (atBottom && !isAtBottom) {
//         // User reached bottom - hide indicator
//         isAtBottom = true;
//         indicator.style.opacity = '0';
//         indicator.style.transform = 'translateX(-50%) scale(0.8)';
//         setTimeout(() => {
//           if (isAtBottom) {
//             indicator.style.display = 'none';
//           }
//         }, 300);
//       } else if (!atBottom && isAtBottom) {
//         // User scrolled up from bottom - show indicator
//         isAtBottom = false;
//         indicator.style.display = 'block';
//         indicator.style.opacity = '1';
//         indicator.style.transform = 'translateX(-50%) scale(1)';
//       }
//     }

//     function smoothScrollToBottom() {
//       let currentScroll = window.scrollY;
//       let targetScroll = document.body.scrollHeight - window.innerHeight;
//       let duration = 8000; // Reduced duration for smoother experience
//       let startTime = null;
//       isScrolling = true;

//       // Keep the same scroll down icon during auto-scrolling
//       indicator.innerHTML = originalText;
//       indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
//       indicator.style.animation = 'pulse 1s infinite';

//       function scrollStep(timestamp) {
//         if (!startTime) startTime = timestamp;
//         let progress = timestamp - startTime;
//         let percentage = Math.min(progress / duration, 1);

//         // Use easeInOutCubic for smoother animation
//         let easePercentage = percentage < 0.5
//           ? 4 * percentage * percentage * percentage
//           : 1 - Math.pow(-2 * percentage + 2, 3) / 2;

//         if (!isScrolling) {
//           resetIndicator();
//           return;
//         }

//         window.scrollTo(0, currentScroll + (targetScroll - currentScroll) * easePercentage);

//         if (progress < duration && percentage < 1) {
//           scrollAnimationFrame = requestAnimationFrame(scrollStep);
//         } else {
//           // Scrolling completed
//           isScrolling = false;
//           resetIndicator();
//           // Check position after auto-scroll completes
//           setTimeout(checkScrollPosition, 100);
//         }
//       }

//       scrollAnimationFrame = requestAnimationFrame(scrollStep);
//     }

//     function resetIndicator() {
//       indicator.innerHTML = originalText;
//       indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
//       indicator.style.animation = 'bounce 1.5s infinite';
//       isScrolling = false;
//     }

//     indicator.addEventListener('click', function() {
//       if (!isScrolling && !isAtBottom) {
//         smoothScrollToBottom();
//       }
//     });

//     // Add scroll event listener to check position
//     window.addEventListener('scroll', function() {
//       if (!isScrolling) {
//         checkScrollPosition();
//       }
//     });

//     function stopScrolling() {
//       if (isScrolling) {
//         isScrolling = false;
//         cancelAnimationFrame(scrollAnimationFrame);
//         resetIndicator();
//         // Check position after stopping
//         setTimeout(checkScrollPosition, 100);
//       }
//     }

//     // Stop scrolling on user interaction
//     ['mousedown', 'wheel', 'keydown', 'touchstart'].forEach(event => {
//       window.addEventListener(event, stopScrolling);
//     });

//     // Initial position check
//     setTimeout(checkScrollPosition, 500);

//     var style = document.createElement('style');
//     style.innerHTML = \`
//       @keyframes bounce {
//         0%, 100% { transform: translateX(-50%) translateY(0); }
//         50% { transform: translateX(-50%) translateY(-10px); }
//       }
//       @keyframes pulse {
//         0%, 100% { transform: translateX(-50%) scale(1); }
//         50% { transform: translateX(-50%) scale(1.05); }
//       }
//     \`;
//     document.head.appendChild(style);
//   })();
// `;

//   const isCloseToBottom = ({layoutMeasurement, contentOffset, contentSize}) => {
//     const paddingToBottom = 20;
//     return (
//       layoutMeasurement.height + contentOffset.y >=
//       contentSize.height - paddingToBottom
//     );
//   };

//   const onLayout = event => {
//     const {x, y, height, width} = event.nativeEvent.layout;
//   };

//   const handleSuccess = async () => {
//     navigation?.replace('LoginScreen');
//   };

//   const handleDeny = () => {
//     if (params.fromScreen === 'Home') {
//       setModal(true);
//     } else {
//       navigation.goBack();
//     }
//   };

//   return (
//     <View
//       style={{
//         flex: 1,
//         marginLeft: 10,
//         marginRight: 10,
//         backgroundColor: theme.colors.brandBackground,
//       }}>
//       <WebView
//         incognito={true}
//         ref={ref => (webviewRef.current = ref)}
//         style={{flex: 1, width: '100%'}}
//         originWhitelist={['*']}
//         injectedJavaScript={scrollIndicatorScript}
//         source={{uri: config[config.STAGING].TERMS_AND_CONDITIONS}}
//         onLayout={onLayout}
//         automaticallyAdjustContentInsets
//         setBuiltInZoomControls={false}
//         onMessage={event => {}}
//         renderLoading={PlainLoader}
//         startInLoadingState
//         scalesPageToFit={true}
//         bounces={false}
//         onScroll={event => {
//           const condition =
//             event.nativeEvent.contentOffset.y * 1.03 >
//             event.nativeEvent.contentSize.height -
//               event.nativeEvent.layoutMeasurement.height;
//           if (condition) {
//             setAccepted(true);
//           } else {
//             setAccepted(false);
//           }
//         }}
//       />
//       <View style={{flex: 0.1, flexDirection: 'row', paddingBottom: 30}}>
//         <TouchableOpacity
//           disabled={inTransact}
//           onPress={handleDeny}
//           style={{
//             backgroundColor: theme.colors.gray,
//             borderRadius: 5,
//             flex: 0.8,
//             justifyContent: 'center',
//             alignItems: 'center',
//           }}>
//           <CustomText
//             style={{fontSize: 14, color: '#FFF', alignSelf: 'center'}}>
//             I deny
//           </CustomText>
//         </TouchableOpacity>
//         <View style={{flex: 0.2}}></View>
//         <TouchableOpacity
//           disabled={!accepted || inTransact}
//           onPress={handleSuccess}
//           style={[
//             {
//               borderRadius: 5,
//               flex: 0.8,
//               justifyContent: 'center',
//               alignItems: 'center',
//               flexDirection: 'row',
//             },
//             accepted
//               ? {backgroundColor: theme.colors.brandPrimary}
//               : {backgroundColor: theme.colors.gray},
//           ]}>
//           <CustomText
//             style={{fontSize: 14, color: '#FFF', alignSelf: 'center'}}>
//             I agree
//           </CustomText>
//           {inTransact && <ActivityIndicator />}
//         </TouchableOpacity>
//       </View>
//       <DefaultModal
//         onBottomButtonPress={() => {
//           setModal(false);
//           HandleLogout(dispatch, false);
//           // navigation.navigate('LoginScreen');
//         }}
//         onBottomButtonText={'Log out'}
//         disablePhoneIcon
//         onTopButtonPress={() => setModal(false)}
//         onTopButtonText={'Continue'}
//         Icon={LogoutIcon}
//         text={'Please accept terms and conditions to continue using the app'}
//         modalVisible={modal}
//         setModalVisible={item => setModal(item)}
//       />
//     </View>
//   );
// };

// export default TermsAndConditions;

import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import {theme} from '../../ui/theme/default/theme';
import {axiosPublic} from '../../../api/axiosPublic';
import config from '../../../config';
import PlainLoader from '../../Plainloader/PlainLoader';
import WebView from 'react-native-webview';
import axios from 'axios';
import {event} from 'react-native-reanimated';
import {axiosPrivate} from '../../../api/axiosPrivate';
import GetPracticeInfo from '../../../helpers/getPracticeInfo';
import {DefaultModal} from '../../ui/components/Modals/DefaultModal';
import HandleLogout from '../../../helpers/logout';
import {useDispatch} from 'react-redux';
import LogoutIcon from './assets/logoutIcon';
import CustomText from '../../ui/components/customText/customText';

const TermsAndConditions = ({route, navigation}) => {
  const [accepted, setAccepted] = useState(false);
  const {width, height} = Dimensions.get('window');
  const webviewRef = useRef();
  const [tac, setTac] = useState('');
  const [modal, setModal] = useState(false);
  const [inTransact, setInTransact] = useState(false);
  const [patientInfo, setPatientInfo] = useState(null);
  const [webViewError, setWebViewError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);

  const {params} = route;
  const dispatch = useDispatch();

  useEffect(() => {
    loadTermsAndConditions();
  }, []);

  const loadTermsAndConditions = async () => {
    try {
      const termsAndCond = await axios.get(
        config[config.STAGING].COGNITO_URL + 'terms-and-conditions',
        {
          timeout: 10000, // 10 second timeout
          headers: {
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache',
          },
        },
      );
      setTac(termsAndCond.data.message);
      setWebViewError(false);
    } catch (error) {
      console.error('Error loading terms and conditions:', error);
      setWebViewError(true);
    }
  };

  const scrollIndicatorScript = `
  (function() {
    try {
      var indicator = document.createElement('div');
      indicator.innerHTML = '⬇ Scroll Down ⬇';
      indicator.style.position = 'fixed';
      indicator.style.bottom = '20px';
      indicator.style.left = '50%';
      indicator.style.transform = 'translateX(-50%)';
      indicator.style.fontSize = '18px';
      indicator.style.fontWeight = 'bold';
      indicator.style.color = '#fff';
      indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
      indicator.style.padding = '12px 18px';
      indicator.style.borderRadius = '30px';
      indicator.style.boxShadow = '0px 4px 10px rgba(0, 0, 0, 0.2)';
      indicator.style.zIndex = '9999';
      indicator.style.cursor = 'pointer';
      indicator.style.transition = 'all 0.3s ease';
      indicator.style.animation = 'bounce 1.5s infinite';
      indicator.style.opacity = '1';
      
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
          document.body.appendChild(indicator);
        });
      } else {
        document.body.appendChild(indicator);
      }

      var isScrolling = false;
      var scrollAnimationFrame;
      var originalText = '⬇ Scroll Down ⬇';
      var isAtBottom = false;

      // Function to check if user is at the bottom of the page
      function checkScrollPosition() {
        try {
          var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          var windowHeight = window.innerHeight;
          var documentHeight = document.documentElement.scrollHeight;
          var threshold = 50;

          var atBottom = scrollTop + windowHeight >= documentHeight - threshold;

          if (atBottom && !isAtBottom) {
            isAtBottom = true;
            indicator.style.opacity = '0';
            indicator.style.transform = 'translateX(-50%) scale(0.8)';
            setTimeout(() => {
              if (isAtBottom && indicator) {
                indicator.style.display = 'none';
              }
            }, 300);
          } else if (!atBottom && isAtBottom) {
            isAtBottom = false;
            indicator.style.display = 'block';
            indicator.style.opacity = '1';
            indicator.style.transform = 'translateX(-50%) scale(1)';
          }
        } catch (e) {
          console.error('Error in checkScrollPosition:', e);
        }
      }

      function smoothScrollToBottom() {
        try {
          let currentScroll = window.scrollY;
          let targetScroll = document.body.scrollHeight - window.innerHeight;
          let duration = 8000;
          let startTime = null;
          isScrolling = true;

          indicator.innerHTML = originalText;
          indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
          indicator.style.animation = 'pulse 1s infinite';

          function scrollStep(timestamp) {
            try {
              if (!startTime) startTime = timestamp;
              let progress = timestamp - startTime;
              let percentage = Math.min(progress / duration, 1);

              let easePercentage = percentage < 0.5
                ? 4 * percentage * percentage * percentage
                : 1 - Math.pow(-2 * percentage + 2, 3) / 2;

              if (!isScrolling) {
                resetIndicator();
                return;
              }

              window.scrollTo(0, currentScroll + (targetScroll - currentScroll) * easePercentage);

              if (progress < duration && percentage < 1) {
                scrollAnimationFrame = requestAnimationFrame(scrollStep);
              } else {
                isScrolling = false;
                resetIndicator();
                setTimeout(checkScrollPosition, 100);
              }
            } catch (e) {
              console.error('Error in scrollStep:', e);
              isScrolling = false;
              resetIndicator();
            }
          }

          scrollAnimationFrame = requestAnimationFrame(scrollStep);
        } catch (e) {
          console.error('Error in smoothScrollToBottom:', e);
        }
      }

      function resetIndicator() {
        try {
          if (indicator) {
            indicator.innerHTML = originalText;
            indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
            indicator.style.animation = 'bounce 1.5s infinite';
          }
          isScrolling = false;
        } catch (e) {
          console.error('Error in resetIndicator:', e);
        }
      }

      indicator.addEventListener('click', function() {
        if (!isScrolling && !isAtBottom) {
          smoothScrollToBottom();
        }
      });

      window.addEventListener('scroll', function() {
        if (!isScrolling) {
          checkScrollPosition();
        }
      });

      function stopScrolling() {
        if (isScrolling) {
          isScrolling = false;
          if (scrollAnimationFrame) {
            cancelAnimationFrame(scrollAnimationFrame);
          }
          resetIndicator();
          setTimeout(checkScrollPosition, 100);
        }
      }

      ['mousedown', 'wheel', 'keydown', 'touchstart'].forEach(event => {
        window.addEventListener(event, stopScrolling, {passive: true});
      });

      setTimeout(checkScrollPosition, 500);

      var style = document.createElement('style');
      style.innerHTML = \`
        @keyframes bounce {
          0%, 100% { transform: translateX(-50%) translateY(0); }
          50% { transform: translateX(-50%) translateY(-10px); }
        }
        @keyframes pulse {
          0%, 100% { transform: translateX(-50%) scale(1); }
          50% { transform: translateX(-50%) scale(1.05); }
        }
      \`;
      document.head.appendChild(style);
    } catch (e) {
      console.error('Error in scroll indicator script:', e);
    }
  })();

  // Send scroll position to React Native
  window.addEventListener('scroll', function() {
    try {
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      var windowHeight = window.innerHeight;
      var documentHeight = document.documentElement.scrollHeight;
      var scrollPercentage = (scrollTop + windowHeight) / documentHeight;
      
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'scroll',
          scrollPercentage: scrollPercentage
        }));
      }
    } catch (e) {
      console.error('Error sending scroll message:', e);
    }
  });
`;

  const isCloseToBottom = ({layoutMeasurement, contentOffset, contentSize}) => {
    const paddingToBottom = 20;
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  const onLayout = event => {
    const {x, y, height, width} = event.nativeEvent.layout;
  };

  const handleSuccess = async () => {
    setInTransact(true);
    try {
      // Add any success logic here if needed
      navigation?.replace('LoginScreen');
    } catch (error) {
      console.error('Error in handleSuccess:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setInTransact(false);
    }
  };

  const handleDeny = () => {
    if (params?.fromScreen === 'Home') {
      setModal(true);
    } else {
      navigation.goBack();
    }
  };

  const handleWebViewError = syntheticEvent => {
    const {nativeEvent} = syntheticEvent;
    console.error('WebView error:', nativeEvent);
    setWebViewError(true);
    setIsLoading(false);
  };

  const handleWebViewLoad = () => {
    setIsLoading(false);
    setWebViewError(false);
  };

  const handleWebViewLoadStart = () => {
    setIsLoading(true);
    setWebViewError(false);
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    setWebViewError(false);
    setIsLoading(true);
    if (webviewRef.current) {
      webviewRef.current.reload();
    }
  };

  const handleWebViewMessage = event => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'scroll') {
        // Use message-based scroll detection as backup
        if (data.scrollPercentage >= 0.95) {
          setAccepted(true);
        } else {
          setAccepted(false);
        }
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };

  const renderErrorView = () => (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      }}>
      <CustomText
        style={{
          fontSize: 18,
          textAlign: 'center',
          marginBottom: 20,
          color: theme.colors.text || '#000',
        }}>
        Unable to load Terms and Conditions
      </CustomText>
      <TouchableOpacity
        onPress={handleRetry}
        style={{
          backgroundColor: theme.colors.brandPrimary,
          padding: 15,
          borderRadius: 8,
          marginBottom: 10,
        }}>
        <CustomText style={{color: '#fff', fontSize: 16}}>
          Retry ({retryCount})
        </CustomText>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={{
          backgroundColor: theme.colors.gray,
          padding: 15,
          borderRadius: 8,
        }}>
        <CustomText style={{color: '#fff', fontSize: 16}}>Go Back</CustomText>
      </TouchableOpacity>
    </View>
  );

  const webViewProps = {
    ref: webviewRef,
    style: {flex: 1, width: '100%'},
    source: {
      uri: config[config.STAGING].TERMS_AND_CONDITIONS,
      headers: {
        'Cache-Control': 'no-cache',
        'User-Agent':
          Platform.OS === 'ios'
            ? 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
            : undefined,
      },
    },
    originWhitelist: ['*'],
    injectedJavaScript: scrollIndicatorScript,
    onLayout: onLayout,
    onMessage: handleWebViewMessage,
    onError: handleWebViewError,
    onHttpError: handleWebViewError,
    onLoad: handleWebViewLoad,
    onLoadStart: handleWebViewLoadStart,
    renderLoading: () => <PlainLoader />,
    startInLoadingState: true,
    scalesPageToFit: Platform.OS === 'android',
    showsHorizontalScrollIndicator: false,
    showsVerticalScrollIndicator: true,
    bounces: false,
    scrollEnabled: true,
    javaScriptEnabled: true,
    domStorageEnabled: true,
    allowsInlineMediaPlayback: true,
    mediaPlaybackRequiresUserAction: false,
    mixedContentMode: 'compatibility',
    thirdPartyCookiesEnabled: false,
    sharedCookiesEnabled: false,
    incognito: true,
    cacheEnabled: false,
    // iOS specific props
    ...(Platform.OS === 'ios' && {
      allowsBackForwardNavigationGestures: false,
      automaticallyAdjustContentInsets: false,
      contentInsetAdjustmentBehavior: 'never',
      scrollEnabled: true,
      directionalLockEnabled: true,
    }),
    onScroll: event => {
      try {
        const condition =
          event.nativeEvent.contentOffset.y * 1.03 >
          event.nativeEvent.contentSize.height -
            event.nativeEvent.layoutMeasurement.height;

        if (condition) {
          setAccepted(true);
        } else {
          setAccepted(false);
        }
      } catch (error) {
        console.error('Error in onScroll:', error);
      }
    },
  };

  return (
    <View
      style={{
        flex: 1,
        marginLeft: 10,
        marginRight: 10,
        backgroundColor: theme.colors.brandBackground,
      }}>
      {webViewError ? renderErrorView() : <WebView {...webViewProps} />}

      <View style={{flex: 0.1, flexDirection: 'row', paddingBottom: 30}}>
        <TouchableOpacity
          disabled={inTransact}
          onPress={handleDeny}
          style={{
            backgroundColor: theme.colors.gray,
            borderRadius: 5,
            flex: 0.8,
            justifyContent: 'center',
            alignItems: 'center',
            opacity: inTransact ? 0.6 : 1,
          }}>
          <CustomText
            style={{fontSize: 14, color: '#FFF', alignSelf: 'center'}}>
            I deny
          </CustomText>
        </TouchableOpacity>

        <View style={{flex: 0.2}}></View>

        <TouchableOpacity
          disabled={!accepted || inTransact}
          onPress={handleSuccess}
          style={[
            {
              borderRadius: 5,
              flex: 0.8,
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
              opacity: !accepted || inTransact ? 0.6 : 1,
            },
            accepted
              ? {backgroundColor: theme.colors.brandPrimary}
              : {backgroundColor: theme.colors.gray},
          ]}>
          <CustomText
            style={{
              fontSize: 14,
              color: '#FFF',
              alignSelf: 'center',
              marginRight: inTransact ? 8 : 0,
            }}>
            I agree
          </CustomText>
          {inTransact && <ActivityIndicator size="small" color="#FFF" />}
        </TouchableOpacity>
      </View>

      <DefaultModal
        onBottomButtonPress={() => {
          setModal(false);
          HandleLogout(dispatch, false);
        }}
        onBottomButtonText={'Log out'}
        disablePhoneIcon
        onTopButtonPress={() => setModal(false)}
        onTopButtonText={'Continue'}
        Icon={LogoutIcon}
        text={'Please accept terms and conditions to continue using the app'}
        modalVisible={modal}
        setModalVisible={item => setModal(item)}
      />
    </View>
  );
};

export default TermsAndConditions;
