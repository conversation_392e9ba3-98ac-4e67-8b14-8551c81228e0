import React, { memo } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import FastImage from 'react-native-fast-image';
import { Theme, useThemeContext } from '../../theme';
import { theme } from '../../theme/default/theme';
import CustomText from '../customText/customText';

export type PatientCardProps = {
  patient: {
    fname?: string;
    lname?: string;
    email?: string;
    mobile?: string;
    dob?: string;
    gender?: string;
    chart_number?: string;
    photo_base64?: string;
    patient_id?: string;
  };
  onPress?: () => void;
  disabled?: boolean;
};

const PatientCard: React.FC<PatientCardProps> = ({
  patient,
  onPress,
  disabled = false,
}) => {
  const { s } = useThemeContext(createStyle);
  
  const patientImg = patient.photo_base64 
    ? `data:image/png;base64,${patient.photo_base64}` 
    : null;
  
  const fullName = `${patient.fname || ''} ${patient.lname || ''}`.trim();
  const initials = `${patient.fname?.[0] || ''}${patient.lname?.[0] || ''}`;

  return (
    <TouchableOpacity
      disabled={disabled}
      onPress={onPress}
      style={s?.container}>
      <View style={s?.block2}>
        {patientImg ? (
          <FastImage source={{ uri: patientImg }} style={s?.image} />
        ) : (
          <View style={s?.avatarPlaceholder}>
            <Text style={s?.avatarText}>{initials}</Text>
          </View>
        )}
        <View style={s?.block3}>
          <View style={s?.textBlock}>
            <CustomText style={s?.boldText} numberOfLines={1}>
              {fullName || 'Unknown Patient'}
            </CustomText>
            <CustomText style={s?.normalText} numberOfLines={1}>
              ID: {patient.chart_number || patient.patient_id || 'N/A'}
            </CustomText>
          </View>
          <View style={s?.detailsBlock}>
            {patient.email && (
              <CustomText style={s?.smallText} numberOfLines={1}>
                {patient.email}
              </CustomText>
            )}
            {patient.mobile && (
              <CustomText style={s?.smallText} numberOfLines={1}>
                {patient.mobile}
              </CustomText>
            )}
            {patient.dob && (
              <CustomText style={s?.smallText} numberOfLines={1}>
                DOB: {patient.dob}
              </CustomText>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const createStyle = (theme: Theme) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.white,
      borderRadius: theme.metrics.x2,
      padding: theme.metrics.x4,
      marginVertical: theme.metrics.x2,
      marginHorizontal: theme.metrics.x4,
      ...theme.shadows.shadowWhite,
    },
    block2: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    block3: {
      flex: 1,
      marginLeft: theme.metrics.x3,
    },
    textBlock: {
      marginBottom: theme.metrics.x2,
    },
    detailsBlock: {
      gap: theme.metrics.x1,
    },
    image: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    avatarPlaceholder: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: theme.colors.brandPrimary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    avatarText: {
      ...theme.fonts.texts.bodyBigSemibold,
      color: theme.colors.white,
      fontSize: 18,
    },
    boldText: {
      ...theme.fonts.texts.bodyBigSemibold,
      color: theme.colors.darkGrey,
    },
    normalText: {
      ...theme.fonts.texts.bodySmallRegular,
      color: theme.colors.darkGrey,
    },
    smallText: {
      ...theme.fonts.texts.bodySmallRegular,
      color: theme.colors.mainGray,
      fontSize: 12,
    },
  });

const MemorizedComponent = memo(PatientCard);
export { MemorizedComponent as PatientCard };
