import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';
import {Header} from '../ui/components/Header';
// import {PatientCard} from '../ui/components/PatientCard/PatientCard';
import CustomText from '../ui/components/customText/customText';
import {ErrorMsgHandler} from '../ui/components/MessageHandler/messageHandler';
import {theme} from '../ui/theme/default/theme';
import {useThemeContext} from '../ui/theme';
import getOnboardedPatients from '../../hooks/getOnboardedPatients';
import {isUserAdmin} from '../../helpers/userRoleHelper';
import HandleLogout from '../../helpers/logout';

const AdminHomeScreen = ({navigation}) => {
  console.log('AdminHomeScreen - Component mounting...');

  const {s} = useThemeContext(createStyle);
  const dispatch = useDispatch();

  // Redux state with safe defaults
  const onboardedPatients = useSelector(
    state => state.OnboardedPatients?.data || [],
  );
  const fetchingPatients = useSelector(
    state => state.OnboardedPatients?.fetching || false,
  );
  const userInfo = useSelector(state => state.userInfo?.userInfo || {});

  // Local state
  const [errorModal, setErrorModal] = useState(false);
  const [responseErrorMsg, setResponseErrorMsg] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // Get practice from user info
  const practice = userInfo?.togglepractice || '';

  const fetchPatients = useCallback(async () => {
    try {
      console.log(
        'AdminHomeScreen - fetchPatients called with practice:',
        practice,
      );
      if (practice) {
        console.log('AdminHomeScreen - Calling getOnboardedPatients...');
        await getOnboardedPatients(
          dispatch,
          setErrorModal,
          setResponseErrorMsg,
          practice,
        );
        console.log('AdminHomeScreen - getOnboardedPatients completed');
      } else {
        console.log(
          'AdminHomeScreen - No practice available, skipping patient fetch',
        );
      }
    } catch (error) {
      console.error('AdminHomeScreen - Error in fetchPatients:', error);
      setResponseErrorMsg('Failed to fetch patients');
      setErrorModal(true);
    }
  }, [dispatch, practice]);
  fetchPatients();

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchPatients();
    setRefreshing(false);
  }, [fetchPatients]);

  // Check user role and redirect non-admin users
  const checkUserRoleAndRedirect = useCallback(async () => {
    try {
      const isAdmin = await isUserAdmin();
      console.log('AdminHomeScreen - isAdmin:', isAdmin);

      // If isAdmin is null, trigger logout for security
      if (isAdmin === null) {
        console.log(
          'Admin panel: User role is null, triggering logout for security',
        );
        await HandleLogout(dispatch, false);
        navigation.reset({
          index: 0,
          routes: [{name: 'OnBoardingScreen'}],
        });
        return;
      }

      // If user is not admin (false), redirect to home
      if (isAdmin === false) {
        navigation.reset({
          index: 0,
          routes: [{name: 'HomeStack'}],
        });
      }

      // If isAdmin is true, user stays on admin panel (no action needed)
    } catch (error) {
      console.error('Error checking user role:', error);
      // On error, trigger logout for security
      console.log(
        'Admin panel: Error checking role, triggering logout for security',
      );
      await HandleLogout(dispatch, false);
      navigation.reset({
        index: 0,
        routes: [{name: 'OnBoardingScreen'}],
      });
    }
  }, [navigation, dispatch]);

  // Fetch patients when screen is focused
  useFocusEffect(
    useCallback(() => {
      checkUserRoleAndRedirect();
      fetchPatients();
    }, [checkUserRoleAndRedirect, fetchPatients]),
  );

  const handlePatientPress = patient => {
    // TODO: Navigate to patient details screen
    console.log('Patient pressed:', patient);
    // navigation.navigate('PatientDetails', { patient });
  };

  const renderPatientCard = ({item}) => {
    try {
      return (
        <View style={s?.errorContainer}>
          <CustomText style={s?.boldText}>
            {item.fname || 'Unknown'} {item.lname || 'Patient'}
          </CustomText>
          <CustomText style={s?.normalText}>
            ID: {item.chart_number || item.patient_id || 'N/A'}
          </CustomText>
          {item.email && (
            <CustomText style={s?.smallText}>{item.email}</CustomText>
          )}
        </View>
      );
    } catch (error) {
      console.error('AdminHomeScreen - Error rendering PatientCard:', error);
      return (
        <View style={s?.errorContainer}>
          <CustomText style={s?.errorText}>
            Error loading patient card
          </CustomText>
        </View>
      );
    }
  };

  const renderEmptyComponent = () => (
    <View style={s?.emptyContainer}>
      <CustomText style={s?.emptyText}>No onboarded patients found</CustomText>
    </View>
  );

  const renderHeader = () => (
    <View style={s?.headerContainer}>
      <CustomText style={s?.titleText}>Onboarded Patients</CustomText>
      <CustomText style={s?.subtitleText}>
        {onboardedPatients.length} patient
        {onboardedPatients.length !== 1 ? 's' : ''} found
      </CustomText>
    </View>
  );

  return (
    <SafeAreaView style={s?.container}>
      <Header
        title="Admin Panel"
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      {fetchingPatients && !refreshing ? (
        <View style={s?.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
          <CustomText style={s?.loadingText}>Loading patients...</CustomText>
        </View>
      ) : (
        <FlatList
          data={onboardedPatients}
          renderItem={renderPatientCard}
          keyExtractor={(item, index) =>
            item.patient_id?.toString() ||
            item.chart_number?.toString() ||
            index.toString()
          }
          ListHeaderComponent={renderHeader}
          ListEmptyComponent={renderEmptyComponent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.brandPrimary]}
            />
          }
          showsVerticalScrollIndicator={true}
          contentContainerStyle={s?.listContainer}
        />
      )}

      {/* <ErrorMsgHandler
        isVisible={errorModal}
        onRequestClose={() => setErrorModal(false)}
        errorMsg={responseErrorMsg}
      /> */}
    </SafeAreaView>
  );
};

const createStyle = theme =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      ...theme.fonts.texts.bodySmallRegular,
      color: theme.colors.darkGrey,
      marginTop: theme.metrics.x3,
    },
    listContainer: {
      paddingBottom: theme.metrics.x4,
    },
    headerContainer: {
      padding: theme.metrics.x4,
      backgroundColor: theme.colors.white,
      marginBottom: theme.metrics.x2,
    },
    titleText: {
      ...theme.fonts.texts.h2,
      color: theme.colors.darkGrey,
      marginBottom: theme.metrics.x2,
    },
    subtitleText: {
      ...theme.fonts.texts.bodySmallRegular,
      color: theme.colors.mainGray,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: theme.metrics.x8,
    },
    emptyText: {
      ...theme.fonts.texts.bodyBigRegular,
      color: theme.colors.mainGray,
      textAlign: 'center',
    },
    errorContainer: {
      padding: theme.metrics.x4,
      backgroundColor: theme.colors.white,
      marginVertical: theme.metrics.x2,
      marginHorizontal: theme.metrics.x4,
      borderRadius: theme.metrics.x2,
      borderColor: '#ff6b6b',
      borderWidth: 1,
    },
    errorText: {
      ...theme.fonts.texts.bodySmallRegular,
      color: '#ff6b6b',
      textAlign: 'center',
    },
    boldText: {
      ...theme.fonts.texts.bodyBigSemibold,
      color: theme.colors.darkGrey,
      marginBottom: theme.metrics.x1,
    },
    normalText: {
      ...theme.fonts.texts.bodySmallRegular,
      color: theme.colors.darkGrey,
      marginBottom: theme.metrics.x1,
    },
    smallText: {
      ...theme.fonts.texts.bodySmallRegular,
      color: theme.colors.mainGray,
      fontSize: 12,
    },
  });

export default AdminHomeScreen;
