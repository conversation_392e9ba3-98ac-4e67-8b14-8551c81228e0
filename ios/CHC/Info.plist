<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>CHC</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>0.7.1</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.coherent.clearhealthcare</string>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>item-apps</string>
		<string>tel</string>
		<string>telprompt</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>clinicweb-dev.clearhealthcare.ai</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>clinicweb-dev.clearhealthcare.ai</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>clinicweb.clearhealthcare.ai</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>clinicweb.clearhealthcare.ai</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>15</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>We need access to your camera to capture and upload images of your insurance information for verification and updating purposes.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location to estimate the time it takes to reach the hospital and provide directions, both when the app is in use and in the background.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We need your location to estimate the time it takes to reach the hospital and provide directions.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location while the app is in use to estimate the time it takes to reach the hospital and provide directions.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app does not require access to the microphone.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to the photo library.</string>
	<key>NSDocumentsFolderUsageDescription</key>
	<string>We need access to save downloaded PDF files to your Documents folder.</string>
	<key>NSDownloadsFolderUsageDescription</key>
	<string>We need access to save downloaded PDF files to your Downloads folder.</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>PDF Document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.adobe.pdf</string>
			</array>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
		</dict>
	</array>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UIAppFonts</key>
	<array>
		<string>Montserrat-Black.ttf</string>
		<string>Montserrat-BlackItalic.ttf</string>
		<string>Montserrat-Bold.ttf</string>
		<string>Montserrat-BoldItalic.ttf</string>
		<string>Montserrat-ExtraBold.ttf</string>
		<string>Montserrat-ExtraBoldItalic.ttf</string>
		<string>Montserrat-Italic.ttf</string>
		<string>Montserrat-Light.ttf</string>
		<string>Montserrat-LightItalic.ttf</string>
		<string>Montserrat-Medium.ttf</string>
		<string>Montserrat-MediumItalic.ttf</string>
		<string>Montserrat-Regular.ttf</string>
		<string>Montserrat-SemiBold.ttf</string>
		<string>Montserrat-Thin.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
