import HandleApiCallErr from '../helpers/handleApiCallError';
import {
  setOnboardedPatientsFetching,
  setOnboardedPatientsList,
} from '../stores/onboardedPatientsReducer';
import { axiosPrivate } from '../api/axiosPrivate';

const { default: config } = require('../config');

const GetOnboardedPatients = (dispatch, setErrorModal, setResponseErrorMsg, practice) => {
  dispatch(setOnboardedPatientsFetching(true));

  const url = `${config[config.STAGING].PATIENT_INFO}get-onboarded-patients`;

  axiosPrivate
    .get(url, {practice })
    .then(response => {
      if (response.data.status === 200) {
        const patients = response.data.patients || [];
        dispatch(setOnboardedPatientsList(patients));
      } else {
        setResponseErrorMsg(response.data.message || 'Error fetching onboarded patients');
        setErrorModal(true);
      }
      dispatch(setOnboardedPatientsFetching(false));
    })
    .catch(err => {
      console.log('Error fetching onboarded patients:', err);
      const errorMessage = HandleApiCallErr(err).message;
      setResponseErrorMsg(errorMessage);
      setErrorModal(true);
      dispatch(setOnboardedPatientsFetching(false));
    });
};

export default GetOnboardedPatients;
