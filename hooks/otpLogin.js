import AsyncStorage from '@react-native-async-storage/async-storage';
import {axiosPublic} from '../api/axiosPublic';
import config from '../config';
import SetPracticeInfo from '../helpers/setPractiveInfo';
import {setToken} from '../stores/tokenReducer';
import {getPatientInfo} from './login';
import messaging from '@react-native-firebase/messaging';
import DeviceInfo from 'react-native-device-info';
import { storeUserRole, getUserRole, clearUserRole } from '../helpers/userRoleHelper';

// Export the helper functions for backward compatibility
export { getUserRole, clearUserRole };

// send otp
export const otpLoginHook = async (
  mobile,
  DOB,
  setError,
  setresponseErrorMsg,
  setSuccessModal,
  setResponseSuccessMsg,
  dispatch,
) => {
  const payload = {
    mobile_number: mobile,
    dob: DOB,
  };
  console.log('Login', payload);

  try {
    const apiResponse = await axiosPublic.post(
      `${config[config.STAGING].COGNITO_URL}mobile-number-checking`,
      payload,
    );
    return apiResponse.data;
  } catch (error) {
    if (error.response.data.status === '500') {
      setError(true);
      setresponseErrorMsg(
        error.response.data.message
          ? error.response.data.message
          : error.response.data
            ? error.response.data.msg
            : error.toString(),
      );
    }
    throw error;
  }
};

async function requestUserPermission() {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;
  return enabled;
}

// verify otp
export const loginOtpConfirmHook = async (
  mobile,
  dob,
  otp,
  setErrorModal,
  setresponseErrorMsg,
  dispatch,
) => {
  try {
    const payload = {
      mobile_number: mobile,
      dob: dob,
      otp: otp,
      role: 'patient',
      version: DeviceInfo.getVersion(),
    };
    const permissionStatus = await requestUserPermission();
    let deviceToken = null;
    if (permissionStatus) {
      try {
        deviceToken = await messaging().getToken();
        console.log('deviceToken is: ', deviceToken);
      } catch (e) {
        console.error('error in fetching device token: ', e);
      }
    }
    console.log('dev', deviceToken);

    const apiResponse = await axiosPublic.post(
      `${config[config.STAGING].COGNITO_URL}mobile-number-otp-verification`,
      payload,
    );
    console.log("apiResponse", apiResponse);

    const data = apiResponse.data;
    const patient_pratices = data.user.user_details.chart;
    const userData = data.user;
    const bitrixConfig = userData.user_details.bitrix_chat;

    // Extract role from API response if available
    const userRole = userData.user_details?.role || userData.role || '';

    // Log user data and role for debugging
    console.log('=== ROLE DEBUG ===');
    console.log('Full API Response:', JSON.stringify(apiResponse.data, null, 2));
    console.log('User Data:', JSON.stringify(userData, null, 2));
    console.log('User Details:', JSON.stringify(userData.user_details, null, 2));
    console.log('Extracted User Role:', userRole);
    console.log('Role from user_details:', userData.user_details?.role);
    console.log('Role from userData:', userData.role);
    console.log('=== END ROLE DEBUG ===');

    await SetPracticeInfo(
      patient_pratices?.rsi?.[0] || '',
      patient_pratices?.sen?.[0] || '',
      patient_pratices?.sen && !patient_pratices.rsi ? 'sen' : 'rsi',
    );
    const token = {
      AccessToken: userData.access_token,
      IdToken: userData.id_token,
      RefreshToken: userData.refresh_token,
      ExpiresBy: userData.expires_by,
      DeviceToken: deviceToken,
      BitrixConfig: bitrixConfig,
    };
    dispatch(setToken(token));
    await AsyncStorage.setItem('access_token', JSON.stringify(token));
    const patientInfo = await getPatientInfo(
      patient_pratices.sen && !patient_pratices.rsi ? 'sen' : 'rsi',
    );

    console.log('=== ROLE STORAGE DEBUG ===');
    console.log('patientInfo exists:', !!patientInfo);
    console.log('patientInfo.userInfo exists:', !!(patientInfo && patientInfo.userInfo));
    console.log('userRole value:', userRole);
    console.log('userRole truthy:', !!userRole);

    // Store role in AsyncStorage regardless of patientInfo structure
    if (userRole) {
      console.log('Storing role in AsyncStorage:', userRole);
      const storeResult = await storeUserRole(userRole);
      console.log('Role storage result:', storeResult);

      // Also add role to patient info if available
      if (patientInfo && patientInfo.userInfo) {
        patientInfo.userInfo.role = userRole;
        console.log('Role added to patientInfo.userInfo');
      } else {
        console.log('patientInfo or userInfo not available, role only stored in AsyncStorage');
      }
    } else {
      console.log('No userRole to store');
    }
    console.log('=== END ROLE STORAGE DEBUG ===');

    return patientInfo;
  } catch (error) {
    console.log('Error from ', error.response.data);
    setresponseErrorMsg(
      error.response.data.msg ? error.response.data.msg : error.toString(),
    );
    setErrorModal(true);
  }
};