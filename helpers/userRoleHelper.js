import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Check if the current user is an admin
 * @returns {Promise<boolean|null>} True if user is admin, false if not admin, null if role not found/error
 */
export const isUserAdmin = async () => {
  try {
    const role = await AsyncStorage.getItem('user_role');
    console.log('isUserAdmin - Retrieved role from AsyncStorage:', role);

    if (role === null || role === undefined) {
      console.log('isUserAdmin - No role found in AsyncStorage, returning null');
      return null;
    }

    const isAdmin = role.toLowerCase() === 'admin';
    console.log('isUserAdmin - Is admin check result:', isAdmin);
    return isAdmin;
  } catch (error) {
    console.error('Error checking if user is admin:', error);
    return null; // Return null on error for security
  }
};

/**
 * Get the current user role
 * @returns {Promise<string|null>} User role or null if not found
 */
export const getCurrentUserRole = async () => {
  try {
    const role = await AsyncStorage.getItem('user_role');
    return role;
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
};

/**
 * Store user role in AsyncStorage
 * @param {string} role - User role to store
 * @returns {Promise<boolean>} True if successful, false otherwise
 */
export const storeUserRole = async (role) => {
  try {
    console.log('storeUserRole - Attempting to store role:', role);
    await AsyncStorage.setItem('user_role', role);

    // Verify the role was stored correctly
    const storedRole = await AsyncStorage.getItem('user_role');
    console.log('storeUserRole - Verification - stored role:', storedRole);

    if (storedRole === role) {
      console.log('storeUserRole - Role stored successfully:', role);
      return true;
    } else {
      console.error('storeUserRole - Role storage verification failed');
      return false;
    }
  } catch (error) {
    console.error('Error storing user role:', error);
    return false;
  }
};

/**
 * Clear user role from AsyncStorage
 * @returns {Promise<boolean>} True if successful, false otherwise
 */
export const clearUserRole = async () => {
  try {
    await AsyncStorage.removeItem('user_role');
    console.log('User role cleared');
    return true;
  } catch (error) {
    console.error('Error clearing user role:', error);
    return false;
  }
};

/**
 * Test function to verify role storage and retrieval
 * @returns {Promise<void>}
 */
export const testRoleStorage = async () => {
  console.log('=== TESTING ROLE STORAGE ===');

  // Test storing admin role
  console.log('Testing admin role storage...');
  await storeUserRole('Admin');
  const adminResult = await isUserAdmin();
  console.log('Admin test result:', adminResult);

  // Test storing patient role
  console.log('Testing patient role storage...');
  await storeUserRole('patient');
  const patientResult = await isUserAdmin();
  console.log('Patient test result:', patientResult);

  // Test clearing role
  console.log('Testing role clearing...');
  await clearUserRole();
  const nullResult = await isUserAdmin();
  console.log('Null test result:', nullResult);

  console.log('=== END ROLE STORAGE TEST ===');
};
